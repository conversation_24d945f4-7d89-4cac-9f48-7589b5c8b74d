import os
import json
import requests
# from dashscope import Generation
import random

from dashscope import Generation
from openai import OpenAI



def get_prompt(path,query,arr1,arr2,arr3,arr4,datetime):
    root_path = os.path.dirname(os.path.abspath(__file__))

    prompt = "".join( open(root_path + "/../prompts/" + path, "r", encoding = "utf-8").readlines() )
    prompt = prompt.replace("{query}", query)
    prompt = prompt.replace("{entitytypelist}", arr1)
    prompt = prompt.replace("{intensionlist}", arr2)
    prompt = prompt.replace("{conditionlist}", arr3)
    prompt = prompt.replace("{Metrics}", arr4)
    prompt = prompt.replace("{datetime}", datetime)

    # 构建 JSON 文件路径
    base_name = path.replace("_template.txt", "")
    json_file = os.path.join("template", base_name + ".json")
    if os.path.exists(json_file):
        with open(json_file, "r", encoding="utf-8") as f:
            json_data = json.load(f)  # 读取 JSON 内容
            json_str = json.dumps(json_data, ensure_ascii=False)  # 转为字符串
            lstname = "{" + base_name + "_list}"
            prompt = prompt.replace(lstname, json_str)  # 替换 {{json名}}

    return prompt


def qwen7b(messages):
    url ="http://192.168.232.20:9018/vllm/predict_async"
    params = {"stream":False,"temperature":"0.1","messages":messages}
    response = requests.request("POST", url, data=json.dumps(params), stream=False,headers={})
    return response.json()["result"]

def qwen14b(messages):
    url ="http://192.168.186.11:8008/vllm/predict_async"
    params = {"stream":False,"temperature":"0.1","messages":messages}
    response = requests.request("POST", url, data=json.dumps(params), stream=False,headers={})
    return response.json()["result"]

def deepseek7b(messages):
    url ="http://192.168.186.11:44949/vllm/predict_async"
    params = {"stream":False,"temperature":"0.8","messages":messages}
    response = requests.request("POST", url, data=json.dumps(params), stream=False,headers={})
    return response.json()["result"]

def qwenMax(messages):
    model = "qwq-32b"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.7,
        enable_search=True
        # tools=changeQwenTools(tools)
    )

    return response.output.choices[0]['message']['content']

def qwenMax2(messages):
    model = "qwen-max"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.7,
        # tools=changeQwenTools(tools)
    )

    return response.output.choices[0]['message']['content']

def qwen72B_sli(messages):
    url = "https://api.siliconflow.cn/v1/chat/completions"

    payload = {
        "model": "Qwen/QwQ-32B",
        "messages": messages,
        "extra_body":{"enable_thinking": False},
        "stream":True
    }
    headers = {
        "Authorization": "Bearer sk-hzixvrxqynemwxcicihfsasslxrjjycyyyekcekldoqjwqes",
        "Content-Type": "application/json"
    }

    response = requests.request("POST", url, json=payload, headers=headers)
    result = json.loads(response.text)
    return result['choices'][0]['message']['content']

def qwen72B(messages):
    model = "qwen2.5-72b-instruct"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.3,
        # tools=changeQwenTools(tools)
    )
    print('API返回内容:', response)
    return response.output.choices[0]['message']['content']

def qwen14B(messages):
    model = "qwen2.5-72b-instruct"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.3,
        # tools=changeQwenTools(tools)
    )

    return response.output.choices[0]['message']['content']

def qwen3(messages):
    model = "qwen3-32b"
    api_key = "sk-c342324d10474284abc452d71d0dd105"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.3,
        enable_thinking =False,
        stream=False,
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "问数专报",
                    "description": "用户请求提供针对某一主题（如企业招标采购数据年度分析）的专门报告，通常这类报告涉及深入的分析和总结 。​",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "问数明细",
                    "description": "用户询问特定项目或事件的具体细节信息，问答助手会针对该问题给出详细且具体的回答，如项目的某个环节进展情况、涉及的具体数据等。",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "问数统计",
                    "description": "用户围绕特定时间段、特定地区、特定版本等条件，询问关于某类数据的统计量（数量、同比、环比等），或数据在一定时间范围内的变化趋势。",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "问数报表",
                    "description": "用户直接索要某个特定时间的特定类型报表，希望获得包含相关数据及分析的完整报表呈现。",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        ]
    )

    return response.output.choices[0]['message']['content']

def deepseekv3(messages):
    model = "deepseek-v3"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.7,
        # tools=changeQwenTools(tools)
    )

    return response.output.choices[0]['message']['content']

client = None

def callKimi(messages) -> str:

    global client
    if client is None:
        client = OpenAI(
            api_key = "sk-NOOv7U6XgWdCwLmkqVbnC14YIJjvzR1ZQNWezNJEC9L29j3S", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
            base_url = "https://api.moonshot.cn/v1",
        )
    # 我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
    # messages.append({
    #     "role": "user",
    #     "content": input,
    # })

    # 携带 messages 与 Kimi 大模型对话
    completion = client.chat.completions.create(
        model="moonshot-v1-8k",
        messages=messages,
        temperature=0,
    )

    # 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
    assistant_message = completion.choices[0].message

    # 为了让 Kimi 大模型拥有完整的记忆，我们必须将 Kimi 大模型返回给我们的消息也添加到 messages 中
    messages.append(assistant_message)

    return assistant_message.content


def get_prompt16(path,query,bodyType,registrationGuidelines):
    root_path = os.path.dirname(os.path.abspath(__file__))

    prompt = "".join( open(root_path + "/../prompts/" + path, "r", encoding = "utf-8").readlines() )
    prompt = prompt.replace("{query}", query)
    prompt = prompt.replace("{bodyType}", bodyType)
    prompt = prompt.replace("{registrationGuidelines}", registrationGuidelines)
    # 构建 JSON 文件路径
    base_name = path.replace("_template.txt", "")
    json_file = os.path.join("template", base_name + ".json")
    if os.path.exists(json_file):
        with open(json_file, "r", encoding="utf-8") as f:
            json_data = json.load(f)  # 读取 JSON 内容
            json_str = json.dumps(json_data, ensure_ascii=False)  # 转为字符串
            lstname = "{" + base_name + "_list}"
            prompt = prompt.replace(lstname, json_str)  # 替换 {{json名}}

    return prompt



def get_rag3_prompt(path,query,Aresult,Bresult):
    root_path = os.path.dirname(os.path.abspath(__file__))

    prompt = "".join( open(root_path + "/../RAGV3/" + path, "r", encoding = "utf-8").readlines() )
    prompt = prompt.replace("{query}", query)
    prompt = prompt.replace("{Aresult}", Aresult)
    prompt = prompt.replace("{Bresult}", Bresult)
    # 构建 JSON 文件路径
    base_name = path.replace("_template.txt", "")
    json_file = os.path.join("template", base_name + ".json")
    if os.path.exists(json_file):
        with open(json_file, "r", encoding="utf-8") as f:
            json_data = json.load(f)  # 读取 JSON 内容
            json_str = json.dumps(json_data, ensure_ascii=False)  # 转为字符串
            lstname = "{" + base_name + "_list}"
            prompt = prompt.replace(lstname, json_str)  # 替换 {{json名}}

    return prompt