import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import groovy.json.JsonBuilder

def main(metrics) {
	// 当前时间
	def currentDate = new Date().format('yyyy-MM-dd')
	// 解析指标结果
	def configErrorMsg = ""
	if(!metrics){
		configErrorMsg = "转换指标失败"
	}
	def jsonBuilder = new JsonBuilder(metrics)

	return [
		errormsg : configErrorMsg,
		metrics : jsonBuilder.toPrettyString(),
		datetime : currentDate
	]
}